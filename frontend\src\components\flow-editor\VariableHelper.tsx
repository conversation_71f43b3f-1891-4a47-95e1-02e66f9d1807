import React, { useState } from 'react';
import { RpaStep } from '@rpa-project/shared';

// Temporary local implementation until shared package is fixed
function extractVariableNames(text: string): string[] {
  if (!text || typeof text !== 'string') {
    return [];
  }

  const matches = text.match(/\$\{([^}]+)\}/g);
  if (!matches) {
    return [];
  }

  return matches.map(match => {
    const variableName = match.slice(2, -1).trim(); // Remove ${ and }
    return variableName;
  });
}

interface VariableHelperProps {
  steps: RpaStep[];
  currentStepIndex: number;
  onInsertVariable: (variableName: string) => void;
  className?: string;
}

export const VariableHelper: React.FC<VariableHelperProps> = ({
  steps,
  currentStepIndex,
  onInsertVariable,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Get all variables defined before the current step
  const availableVariables = React.useMemo(() => {
    const variables: { name: string; stepIndex: number; stepType: string; description?: string }[] = [];

    for (let i = 0; i < currentStepIndex; i++) {
      const step = steps[i];

      // Check if this step type creates variables
      const variableCreatingSteps = [
        'extractText',
        'extractAttribute',
        'takeScreenshot',
        'downloadFile',
        'extractPdfValues',
        'processWithLLM',
        'apiCall',
        'apiAuth',
        'fortnoxCreateVoucher'
      ];

      if (variableCreatingSteps.includes(step.type)) {
        const extractStep = step as any;
        if (extractStep.variableName) {
          variables.push({
            name: extractStep.variableName,
            stepIndex: i,
            stepType: step.type,
            description: step.description
          });
        }
      }
    }

    return variables;
  }, [steps, currentStepIndex]);

  return (
    <div className={`variable-helper ${className}`}>
      <button
        type="button"
        onClick={() => setIsExpanded(!isExpanded)}
        style={{
          background: 'none',
          border: '1px solid #d1d5db',
          borderRadius: '0.375rem',
          padding: '0.25rem 0.5rem',
          fontSize: '0.75rem',
          color: availableVariables.length === 0 ? '#9ca3af' : '#6b7280',
          cursor: 'pointer',
          display: 'flex',
          alignItems: 'center',
          gap: '0.25rem',
          opacity: availableVariables.length === 0 ? 0.7 : 1
        }}
      >
        <span>📝</span>
        <span>Variabler ({availableVariables.length})</span>
        {availableVariables.length > 0 && (
          <span style={{ transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)', transition: 'transform 0.2s' }}>
            ▼
          </span>
        )}
      </button>

      {isExpanded && (
        <div style={{
          marginTop: '0.5rem',
          border: '1px solid #d1d5db',
          borderRadius: '0.375rem',
          backgroundColor: '#ffffff',
          padding: '0.5rem',
          maxHeight: '200px',
          overflowY: 'auto'
        }}>
          {availableVariables.length === 0 ? (
            <div style={{
              fontSize: '0.75rem',
              color: '#9ca3af',
              padding: '0.375rem',
              backgroundColor: '#f9fafb',
              borderRadius: '0.25rem',
              fontStyle: 'italic',
              textAlign: 'center'
            }}>
              ℹ️ Inga variabler tillgängliga än. Lägg till steg som skapar variabler (t.ex. Extract Text, Take Screenshot) före detta steg.
            </div>
          ) : (
            <>
              <div style={{
                fontSize: '0.75rem',
                color: '#6b7280',
                marginBottom: '0.5rem',
                fontWeight: '500'
              }}>
                Tillgängliga variabler:
              </div>

              {availableVariables.map((variable, index) => (
                <div
                  key={index}
                  style={{
                    padding: '0.375rem',
                    borderRadius: '0.25rem',
                    marginBottom: '0.25rem',
                    backgroundColor: '#f9fafb',
                    border: '1px solid #e5e7eb',
                    cursor: 'pointer',
                    transition: 'background-color 0.15s'
                  }}
                  onClick={() => onInsertVariable(variable.name)}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#f3f4f6';
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#f9fafb';
                  }}
                >
                  <div style={{
                    fontSize: '0.875rem',
                    fontWeight: '500',
                    color: '#1f2937',
                    fontFamily: 'monospace'
                  }}>
                    ${'{' + variable.name + '}'}
                  </div>
                  <div style={{
                    fontSize: '0.75rem',
                    color: '#6b7280',
                    marginTop: '0.125rem'
                  }}>
                    Från steg {variable.stepIndex + 1}: {variable.stepType}
                    {variable.description && ` - ${variable.description}`}
                  </div>
                </div>
              ))}

              <div style={{
                fontSize: '0.75rem',
                color: '#9ca3af',
                marginTop: '0.5rem',
                padding: '0.375rem',
                backgroundColor: '#f9fafb',
                borderRadius: '0.25rem',
                fontStyle: 'italic'
              }}>
                💡 Klicka på en variabel för att infoga den som ${'{variableName}'}
              </div>
            </>
          )}
        </div>
      )}
    </div>
  );
};

/**
 * Hook to get variable suggestions for autocomplete
 */
export const useVariableSuggestions = (steps: RpaStep[], currentStepIndex: number) => {
  return React.useMemo(() => {
    const variables: string[] = [];

    for (let i = 0; i < currentStepIndex; i++) {
      const step = steps[i];

      // Check if this step type creates variables
      const variableCreatingSteps = [
        'extractText',
        'extractAttribute',
        'takeScreenshot',
        'downloadFile',
        'extractPdfValues',
        'processWithLLM',
        'apiCall',
        'apiAuth',
        'fortnoxCreateVoucher'
      ];

      if (variableCreatingSteps.includes(step.type)) {
        const extractStep = step as any;
        if (extractStep.variableName) {
          variables.push(extractStep.variableName);
        }
      }
    }

    return variables;
  }, [steps, currentStepIndex]);
};

/**
 * Utility function to validate variable references in text
 */
export const validateVariableReferences = (
  text: string,
  availableVariables: string[]
): { valid: boolean; missingVariables: string[] } => {
  const referencedVariables = extractVariableNames(text);
  const missingVariables = referencedVariables.filter(varName => !availableVariables.includes(varName));

  return {
    valid: missingVariables.length === 0,
    missingVariables
  };
};
